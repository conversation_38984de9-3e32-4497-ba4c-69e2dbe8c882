import {
    <PERSON><PERSON>,
    <PERSON>alogContent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
} from "@/components/ui/dialog";
import type { RenderError } from '@react-pdf-viewer/core';
import { SpecialZoomLevel, Viewer, Worker } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';
import { printPlugin } from '@react-pdf-viewer/print';
import { Loader2 } from "lucide-react";
import React, { useCallback, useEffect, useState } from 'react';

import { exportPdf } from '@/api/common-api';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import '@react-pdf-viewer/print/lib/styles/index.css';
import { toast } from 'sonner';

// 添加自定义样式
import './pdf-modal.css';

interface PDFViewerButtonProps {
    pdfUrl: string;
    title?: string;
    orderNos?: string[];
}

const PDFViewerModal: React.FC<PDFViewerButtonProps> = ({ pdfUrl, title, orderNos }) => {
    const [open, setOpen] = useState(true);
    const [pdfBlobUrl, setPdfBlobUrl] = useState<string | null>(null);
    const [isLoading] = useState(false);
    const [error] = useState<string | null>(null);

    // 配置打印插件
    const printPluginInstance = printPlugin();

    // 配置默认布局插件
    const defaultLayoutPluginInstance = defaultLayoutPlugin({
        sidebarTabs: () => [],
        toolbarPlugin: {
            fullScreenPlugin: {
                onEnterFullScreen: () => { },
                onExitFullScreen: () => { },
            },
        },
    });

    useEffect(() => {
        let blobUrl: string | null = null;

        async function fetchPdf() {
            try {
                const blob = await exportPdf(pdfUrl);
                blobUrl = URL.createObjectURL(blob.data);
                setPdfBlobUrl(blobUrl);
            } catch (error) {
                console.error('Error fetching PDF:', error);
                toast.error('加载PDF失败: ' + (error as Error).message);
            }
        }

        if (!pdfBlobUrl) {
            fetchPdf();
        }

        return () => {
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [pdfUrl]);

    const renderError: RenderError = useCallback(
        (error) => {
            console.error('PDF viewer error:', error);
            return (
                <div className="flex items-center justify-center h-full text-red-500">
                    PDF加载失败: {error.message || '未知错误'}
                </div>
            );
        },
        []
    );

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="max-w-[90vw] max-h-[90vh] w-full h-full">
                <DialogHeader className="border-b pb-4 mb-4">
                    <DialogTitle className="text-4xl font-bold text-gray-900 dark:text-gray-100">
                        {title ? title : 'PDF 预览'}
                    </DialogTitle>
                    {orderNos && orderNos.length > 0 && (
                        <div className="mt-2 space-y-1">
                            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                                订单号:
                            </p>
                            <div className="flex flex-wrap gap-2">
                                {orderNos.map((orderNo, index) => (
                                    <span
                                        key={index}
                                        className="inline-flex items-center text-2xl px-3 py-1 rounded-full font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 border border-blue-200 dark:border-blue-700"
                                    >
                                        {orderNo}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                </DialogHeader>
                <div className="h-[calc(75vh-140px)] pdf-viewer-container">
                    {isLoading && (
                        <div className="flex items-center justify-center h-full">
                            <Loader2 className="h-8 w-8 animate-spin" />
                            <span className="ml-2">正在加载PDF...</span>
                        </div>
                    )}
                    {error && (
                        <div className="flex items-center justify-center h-full text-red-500">
                            {error}
                        </div>
                    )}
                    {pdfBlobUrl && !isLoading && !error && (
                        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
                            <div style={{ height: '100%' }}>
                                <Viewer
                                    fileUrl={pdfBlobUrl}
                                    plugins={[defaultLayoutPluginInstance, printPluginInstance]}
                                    defaultScale={SpecialZoomLevel.PageFit}
                                    renderError={renderError}
                                />
                            </div>
                        </Worker>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default PDFViewerModal;