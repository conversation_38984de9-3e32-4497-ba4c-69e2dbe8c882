import { DialogContent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { SamePackageOrder } from "@/api/supplier/supplier-order/supplier-order-package-api"

export const TipSupplierPackageModals = ({ content, packageContent }: { content: string, packageContent?: SamePackageOrder }) => {
    return (
        <DialogContent className="max-w-2xl">
            <DialogHeader>
                <DialogTitle>
                    提示
                </DialogTitle>
            </DialogHeader>

            <div className="text-center font-bold text-lg text-red-500">
                {content}
            </div>

            {packageContent && (
                <div className="mt-4">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <div className="text-sm font-medium">已扫描订单:</div>
                            <div className="grid grid-cols-1 gap-2">
                                {Object.entries(packageContent.completed).map(([orderNo, order]) => (
                                    <div
                                        key={orderNo}
                                        className="bg-primary/10 text-primary rounded px-2 py-1"
                                    >
                                        <div>
                                            {orderNo} ({order.scanCount}/{order.count})
                                        </div>
                                        <div className="text-xs font-bold text-muted-foreground">{order.title}</div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* 未完成订单 */}
                        {Object.keys(packageContent.nonCompleted).length > 0 && (
                            <div className="space-y-2">
                                <div className="text-sm font-medium text-muted-foreground">
                                    待扫描订单:
                                </div>
                                <div className="grid grid-cols-1 gap-2">
                                    {Object.entries(packageContent.nonCompleted).map(([orderNo, order]) => (
                                        <div
                                            key={orderNo}
                                            className="border-2 border-dashed border-muted-foreground/50 rounded px-2 py-1 text-muted-foreground"
                                        >
                                            <div>
                                                {orderNo} ({order.scanCount}/{order.count})
                                            </div>
                                            <div className="text-xs font-bold text-muted-foreground">{order.title}</div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}
        </DialogContent>
    )
}
