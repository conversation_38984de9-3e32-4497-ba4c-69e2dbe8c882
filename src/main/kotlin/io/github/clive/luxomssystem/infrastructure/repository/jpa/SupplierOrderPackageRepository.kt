package io.github.clive.luxomssystem.infrastructure.repository.jpa

import io.github.clive.luxomssystem.domain.supplierOrder.model.PackageStatus
import io.github.clive.luxomssystem.domain.supplierOrder.model.SupplierOrderPackage
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface SupplierOrderPackageRepository : JpaRepository<SupplierOrderPackage, Long> {
    @Query(
        """
        select sop.* from  supplier_order_packages sop
        where sop.supplier_id = :supplierId
        and (:orderNo is null  or :orderNo = ANY(sop.order_nos))
        order by sop.created_at desc
    """,
        nativeQuery = true,
    )
    fun findBySupplierId(
        supplierId: Long,
        orderNo: String?,
        pageAble: Pageable,
    ): Page<SupplierOrderPackage>

    @Query(
        """
        select sop.* from  supplier_order_packages sop
        where (:orderNo is null  or :orderNo = ANY(sop.order_nos)) order by sop.created_at desc
    """,
        nativeQuery = true,
    )
    fun findByOrderNo(
        orderNo: String?,
        pageAble: Pageable,
    ): Page<SupplierOrderPackage>

    fun findBySupplierIdAndStatusIn(
        supplierId: Long,
        status: List<PackageStatus>,
    ): List<SupplierOrderPackage>
}
