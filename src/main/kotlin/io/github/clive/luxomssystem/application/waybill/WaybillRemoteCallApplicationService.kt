package io.github.clive.luxomssystem.application.waybill

import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WaybillRemoteCallRecord
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRemoteCallRecordRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.event.EventListener
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service

@Service
class WaybillRemoteCallApplicationService(
    private val waybillRepository: WaybillRepository,
    private val waybillRemoteCallRecordRepository: WaybillRemoteCallRecordRepository,
) {
    fun listRemoteCallRecords(waybillId: Long): List<WaybillRemoteCallRecord> =
        waybillRemoteCallRecordRepository.findByWaybillId(waybillId, Sort.by(Sort.Direction.DESC, "createdAt"))

    @EventListener(WaybillCompletedEvent::class)
    fun handleWaybillCompletionEvent(waybillCompletedEvent: WaybillCompletedEvent) {
        val startTime = System.currentTimeMillis()

        log.info {
            """运单完成事件处理开始 | WaybillId: ${waybillCompletedEvent.waybillId} 
        |事件类型: 存储到运单远程调用记录表
            """.trimMargin()
        }

        try {
            waybillRepository.findByIdOrNull(waybillCompletedEvent.waybillId)?.let { waybill ->
                val record =
                    WaybillRemoteCallRecord(
                        waybillId = waybill.id,
                        waybillNo = waybill.waybillNo!!,
                        waybillLabelUrl = waybill.shipping.waybillLabelUrl!!,
                        shipMethod = waybill.shipping.shipMethod!!,
                        channel = waybill.shipping.channel,
                        createdBy = waybill.updatedBy,
                    )
                waybillRemoteCallRecordRepository.saveAndFlush(record)

                val processingTime = System.currentTimeMillis() - startTime
                log.info {
                    """运单完成事件处理成功 | WaybillId: ${waybill.id} 
                |运单号: ${waybill.waybillNo}
                |配送方式: ${waybill.shipping.shipMethod}
                |配送渠道: ${waybill.shipping.channel ?: "N/A"}
                |处理人: ${waybill.updatedBy}
                |处理耗时: ${processingTime}ms
                    """.trimMargin()
                }
            } ?: run {
                log.warn {
                    """运单完成事件处理失败 | WaybillId: ${waybillCompletedEvent.waybillId}
                |错误原因: 运单不存在
                |处理耗时: ${System.currentTimeMillis() - startTime}ms
                    """.trimMargin()
                }
            }
        } catch (e: Exception) {
            log.error {
                """运单完成事件处理异常 | WaybillId: ${waybillCompletedEvent.waybillId}
            |异常类型: ${e.javaClass.simpleName}
            |异常信息: ${e.message}
            |处理耗时: ${System.currentTimeMillis() - startTime}ms
                """.trimMargin()
            }
            throw e
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
