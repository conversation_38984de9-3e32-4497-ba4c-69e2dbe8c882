package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.PageReq
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.exception.CognitionWebException
import io.github.clive.luxomssystem.common.exception.OmsBaseErrorCode
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.common.utils.OpenAPIKeyGenerator
import io.github.clive.luxomssystem.domain.Customer
import io.github.clive.luxomssystem.facade.order.waybill.FinanceStatisticResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.CustomerRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.clive.luxomssystem.infrastructure.repository.redis.CustomerRedisRepository
import io.github.clive.luxomssystem.nextId
import org.springframework.stereotype.Service

@Service
class CustomerApplicationService(
    private val repository: CustomerRepository,
    private val waybillRepository: WaybillRepository,
    private val customerRedisRepository: CustomerRedisRepository,
) {
    fun pageQuery(page: PageReq): PageResponse<Customer> =
        repository
            .findByNameLikeAndStatusInOrderByCreatedAtDesc(
                null,
                listOf(),
                page.toPageable(),
                UserContextHolder.user!!.bizId,
            ).toResponse()

    fun addCustomer(req: AddCustomerRequest) {
        Customer()
            .apply {
                id = nextId()
                name = req.name
                email = req.email
                bizId = UserContextHolder.user!!.bizId
            }.let {
                repository.saveAndFlush(it)
            }
    }

    fun listCustomers(): List<Customer> = repository.findByStatusAndBizId(BaseStatus.ENABLED, UserContextHolder.user!!.bizId)

    fun deleteCustomer(id: Long) {
        repository.deleteById(id)
    }

    fun changeStatus(
        id: Long,
        status: BaseStatus,
    ) {
        repository.findById(id).ifPresent {
            it.status = status
            repository.saveAndFlush(it)
        }
    }

    fun getCustomer(id: Long): Customer = repository.findByIdAndBizId(id, UserContextHolder.user!!.bizId) ?: throw NoSuchElementException()

    fun getCustomerFinancial(id: Long): FinanceStatisticResponse? {
        val customer =
            repository.findByIdAndBizId(id, UserContextHolder.user!!.bizId)
                ?: throw NoSuchElementException("客户不存在")
        return waybillRepository.getFinanceStatisticByCustomer(customer.id, customer.bizId)
    }

    fun generateOpenApiKey(id: Long): String {
        val customer =
            repository.findByIdAndBizId(id, UserContextHolder.user!!.bizId) ?: throw CognitionWebException(
                OmsBaseErrorCode.CUSTOMER_NOT_FOUND,
            )
        val openapiKey = OpenAPIKeyGenerator.generateKey()
        customer.openapiKey = openapiKey
        repository.saveAndFlush(customer)
        customerRedisRepository.saveOpenApiKey(id, openapiKey)
        return openapiKey
    }
}

data class AddCustomerRequest(
    val name: String,
    val email: String,
)
