package io.github.clive.luxomssystem.application.inventory.spu

import SPUExcelRow
import com.alibaba.excel.EasyExcel
import com.alibaba.excel.write.metadata.style.WriteCellStyle
import com.alibaba.excel.write.metadata.style.WriteFont
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy
import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy
import io.github.clive.luxomssystem.application.inventory.sku.SkuApplicationService
import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.domainservice.SPUDomainService
import io.github.clive.luxomssystem.domain.spu.event.SPUCreatedEvent
import io.github.clive.luxomssystem.domain.spu.event.SPUDeletedEvent
import io.github.clive.luxomssystem.domain.spu.event.SPUUpdatedEvent
import io.github.clive.luxomssystem.domain.spu.model.Spu
import io.github.clive.luxomssystem.facade.sku.response.SkuResponse
import io.github.clive.luxomssystem.facade.spu.request.CreateSPURequest
import io.github.clive.luxomssystem.facade.spu.request.PageSPURequest
import io.github.clive.luxomssystem.facade.spu.request.UpdateSPURequest
import io.github.clive.luxomssystem.facade.spu.response.SPUPageResponse
import io.github.clive.luxomssystem.facade.spu.response.SPUResponse
import io.github.clive.luxomssystem.facade.spu.response.SpuSelectResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.CustomerSpuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SupplierSpuRepository
import io.github.clive.luxomssystem.nextId
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.VerticalAlignment
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import toSpuExcelRow
import java.io.ByteArrayOutputStream
import java.io.InputStream

@Service
class SPUApplicationService(
    private val spuRepository: SPURepository,
    private val spuDomainService: SPUDomainService,
    private val eventPublisher: ApplicationEventPublisher,
    private val skuRepository: SkuRepository,
    private val skuApplicationService: SkuApplicationService,
    private val supplierSpuRepository: SupplierSpuRepository,
    private val customerSpuRepository: CustomerSpuRepository,
) {
    @Transactional
    fun createSPU(request: CreateSPURequest): SPUPageResponse {
        val user = UserContextHolder.user!!
        val spu =
            Spu(
                id = nextId(),
                bizId = UserContextHolder.user!!.bizId,
                spuCode = request.spuCode.trim(),
                title = request.title,
                name = request.name,
                cnName = request.cnName,
                category = request.category,
                productImage = request.productImage,
                packageQuantity = request.packageQuantity,
                showImages = request.showImages.toMutableList(),
                description = request.description,
                sizes = request.sizes.toMutableList(),
                colors = request.colors.toMutableList(),
                hsCode = request.hsCode,
            ).apply {
                createdByName = user.name
                updatedByName = user.name
            }

        spuDomainService.validateSPU(spu)
        spuDomainService.performInitialSetup(spu)

        val savedSPU = spuRepository.saveAndFlush(spu)
        eventPublisher.publishEvent(SPUCreatedEvent(savedSPU.id))

        request.skus.map {
            it.spuId = savedSPU.id
            skuApplicationService.createSku(it)
        }

        return SPUPageResponse.fromDomain(savedSPU)
    }

    @Transactional(readOnly = true)
    fun getSPU(id: Long): SPUResponse {
        val spu =
            spuRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("SPU not found with id: $id")
        return SPUResponse.fromDomain(spu, getSPUSkus(id))
    }

    @Transactional
    fun updateSPU(
        id: Long,
        request: UpdateSPURequest,
    ): SPUPageResponse {
        val user = UserContextHolder.user!!

        val spu =
            spuRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("SPU not found with id: $id")

        spu.apply {
            spuCode = request.spuCode
            title = request.title
            name = request.name
            cnName = request.cnName
            category = request.category
            productImage = request.productImage
            packageQuantity = request.packageQuantity
            showImages = request.showImages.toMutableList()
            description = request.description
            sizes = request.sizes.toMutableList()
            colors = request.colors.toMutableList()
            hsCode = request.hsCode
            updatedByName = user.name
            updatedAt = System.currentTimeMillis()
        }

        spuDomainService.validateSPU(spu)

        val updatedSPU = spuRepository.saveAndFlush(spu)

        skuRepository.deleteBySpuIdAndSkuCodeNotIn(spu.id, request.skus.map { it.skuCode })

        request.skus.map {
            it.spuId = updatedSPU.id
            if (it.id == null) {
                skuApplicationService.createSku(it)
            } else {
                skuApplicationService.updateSku(it.id, it)
            }
        }

        // delete skus that are not in the request

        eventPublisher.publishEvent(SPUUpdatedEvent(updatedSPU.id))
        return SPUPageResponse.fromDomain(updatedSPU)
    }

    @Transactional
    fun deleteSPU(id: Long) {
        val spu =
            spuRepository.findByIdOrNull(id)
                ?: throw NoSuchElementException("SPU not found with id: $id")

        spuRepository.delete(spu)
        eventPublisher.publishEvent(SPUDeletedEvent(id))
        skuRepository.deleteBySpuId(id)
    }

    fun pageQuery(req: PageSPURequest): PageResponse<SPUPageResponse> {
        val pageable = req.toPageable()
        val page =
            spuRepository.pageQuery(
                req.spuCode,
                req.category,
                pageable,
                UserContextHolder.user!!.bizId,
                req.title,
            )
        return page.map { SPUPageResponse.fromDomain(it) }.toResponse()
    }

    fun getSPUSkus(id: Long): List<SkuResponse> = skuRepository.findBySpuId(id).map { SkuResponse.fromDomain(it) }.toList()

    fun list(supplierId: Long?): List<SpuSelectResponse> {
        if (supplierId != null) {
            val findBySupplierId = supplierSpuRepository.findBySupplierId(supplierId)
            return spuRepository.findAllIdAndNam(UserContextHolder.user!!.bizId).filter {
                it.id !in findBySupplierId
            }
        } else {
            return spuRepository.findAllIdAndNam(UserContextHolder.user!!.bizId)
        }
    }

    fun listCustomer(customerId: Long?): Any {
        if (customerId != null) {
            val findBySupplierId = customerSpuRepository.findByCustomerId(customerId)
            return spuRepository.findAllIdAndNam(UserContextHolder.user!!.bizId).filter {
                it.id !in findBySupplierId
            }
        } else {
            return spuRepository.findAllIdAndNam(UserContextHolder.user!!.bizId)
        }
    }

    fun generateSPUExcel(ids: List<Long>): ByteArray {
        if (ids.isEmpty()) {
            throw IllegalArgumentException("需要导出的内容不能为空")
        }
        val spus = spuRepository.exportExcel(UserContextHolder.user!!.bizId, ids)

        val data =
            spus.map {
                val skus = skuRepository.findBySpuId(it.id)
                it.toSpuExcelRow(skus)
            }

        // Create cell style for content
        val contentWriteCellStyle = WriteCellStyle()
        contentWriteCellStyle.verticalAlignment = VerticalAlignment.CENTER

        // Create cell style for header
        val headerWriteCellStyle = WriteCellStyle()
        headerWriteCellStyle.verticalAlignment = VerticalAlignment.CENTER
        val headerFont = WriteFont()
        headerFont.fontHeightInPoints = 14 // Increase font size for header
        headerWriteCellStyle.writeFont = headerFont

        val horizontalCellStyleStrategy =
            HorizontalCellStyleStrategy(headerWriteCellStyle, contentWriteCellStyle)

        val outputStream = ByteArrayOutputStream()

        EasyExcel
            .write(outputStream, SPUExcelRow::class.java)
            .registerWriteHandler(
                object : AbstractRowHeightStyleStrategy() {
                    override fun setHeadColumnHeight(
                        row: Row,
                        relativeRowIndex: Int,
                    ) {
                        row.height =
                            (row.height * 1)
                                .toShort() // Increase header row height by 1.5
                        // times
                    }

                    override fun setContentColumnHeight(
                        row: Row,
                        relativeRowIndex: Int,
                    ) {
                        row.height =
                            (row.height * 6)
                                .toShort() // Increase content row height by 1.5
                        // times
                    }
                },
            ).registerWriteHandler(LongestMatchColumnWidthStyleStrategy())
//            .registerWriteHandler(
//                object : AbstractColumnWidthStyleStrategy() {
//                    override fun setColumnWidth(
//                        writeSheetHolder: WriteSheetHolder,
//                        cellDataList: List<WriteCellData<*>>,
//                        cell: Cell,
//                        head: Head,
//                        relativeRowIndex: Int,
//                        isHead: Boolean
//                    ) {
//                        val columnWidth = cell.sheet.getColumnWidth(cell.columnIndex)
//                        cell.sheet.setColumnWidth(
//                            cell.columnIndex,
//                            (columnWidth * 1.5).toInt()
//                        )
//                    }
//                }
//            )
            .registerWriteHandler(horizontalCellStyleStrategy)
            .sheet("SPU List")
            .doWrite(data)

        return outputStream.toByteArray()
    }

    @Transactional
    fun importSPUFromExcel(inputStream: InputStream): Pair<Int, Int> {
        val excelRows =
            EasyExcel
                .read(inputStream)
                .head(SPUExcelImportRow::class.java)
                .sheet()
                .doReadSync<SPUExcelImportRow>()

        var spuCount = 0
        var skuCount = 0

        excelRows.forEach { row ->
            val spu =
                Spu(
                    id = nextId(),
                    bizId = UserContextHolder.user!!.bizId,
                    spuCode = row.spuCode,
                    title = row.title,
                    name = row.name,
                    cnName = row.cnName,
                    category = row.category,
                    productImage = row.productImage,
                    packageQuantity = row.packageQuantity,
                    showImages = row.showImages.split(",").toMutableList(),
                    description = row.description,
                    sizes = row.sizes.split(",").toMutableList(),
                    colors = row.colors.split(",").toMutableList(),
                    hsCode = row.hsCode.ifBlank { null },
                )

            spuDomainService.validateSPU(spu)
            spuDomainService.performInitialSetup(spu)
            val savedSPU = spuRepository.saveAndFlush(spu)
            eventPublisher.publishEvent(SPUCreatedEvent(savedSPU.id))
            spuCount++

            val sizes = row.sizes.split(",")
            val colors = row.colors.split(",")

            sizes.forEach { size ->
                colors.forEach { color ->
                    val sku =
                        Sku(
                            spuId = savedSPU.id,
                            skuCode = "${row.spuCode}-$size-$color",
                            size = size,
                            color = color,
                            purchaseCost = row.purchaseCost,
                            purchaseCostCurrency = row.purchaseCostCurrency,
                            weight = row.weight,
                            volume = row.volume,
                            salePrice = row.salePrice,
                        )
                    skuRepository.save(sku)
                    skuCount++
                }
            }
        }

        return Pair(spuCount, skuCount)
    }
}
