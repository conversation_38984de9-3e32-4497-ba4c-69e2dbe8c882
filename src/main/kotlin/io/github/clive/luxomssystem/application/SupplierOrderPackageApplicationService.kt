package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.PageResponse
import io.github.clive.luxomssystem.common.toPageable
import io.github.clive.luxomssystem.common.toResponse
import io.github.clive.luxomssystem.domain.supplierOrder.model.*
import io.github.clive.luxomssystem.domain.waybill.event.WaybillOutBoundEvent
import io.github.clive.luxomssystem.facade.order.supplierorder.CreatePackageResponse
import io.github.clive.luxomssystem.facade.order.supplierorder.ScanPackageResponse
import io.github.clive.luxomssystem.facade.order.supplierorder.request.PageQuerySubOrderPackageRequest
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.*
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZonedDateTime

@Service
class SupplierOrderPackageApplicationService(
    private val waybillRepository: WaybillRepository,
    private val subOrderRepository: SubOrderRepository,
    private val supplierOrderRepository: SupplierOrderRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val supplierOrderPackageRepository: SupplierOrderPackageRepository,
) {
    @Transactional(rollbackFor = [Exception::class])
    fun createPackage(req: CreatePackageResponse) {
        val user = UserContextHolder.user!!
        log.info { "createPackage user:$user" }
        if (user.supplierId == null) {
            throw IllegalArgumentException("只有供应商才能创建包裹")
        }
        val supplierId = user.supplierId!!
        val packages =
            supplierOrderPackageRepository.findBySupplierIdAndStatusIn(
                supplierId,
                listOf(PackageStatus.CREATED, PackageStatus.PROCESSING),
            )
        if (packages.isNotEmpty()) {
            throw IllegalArgumentException("请先完成之前的包裹")
        }
        val supplierOrderPackage =
            SupplierOrderPackage(
                id = nextId(),
                packageNo = req.name,
                supplierId = supplierId,
                packageContent = mutableMapOf(),
            ).apply {
                this.createdByName = user.name
                this.updatedByName = user.name
                this.createdBy = user.id
                this.updatedBy = user.id
            }
        supplierOrderPackageRepository.saveAndFlush(supplierOrderPackage)

        log.info { "createPackage success: $supplierOrderPackage" }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun scanPackage(
        orderNo: String,
        packageId: Long,
    ): ScanPackageResponse {
        val startTime = System.currentTimeMillis()
        val user = UserContextHolder.user!!
        log.info { "开始扫描包裹 | 用户ID: ${user.id} | 业务ID: ${user.bizId} | 订单号: $orderNo | 包裹ID: $packageId" }

        val supplierOrderPackage =
            supplierOrderPackageRepository.findByIdOrNull(packageId)
                ?: throw IllegalArgumentException("包裹不存在： $packageId")

        val supplierOrders = supplierOrderRepository.queryByOrderNoAndBizId(orderNo, user.bizId)
        val supplierOrder: SupplierOrder
        if (supplierOrders.isEmpty()) {
            log.warn { "订单查询失败 | 订单号: $orderNo | 业务ID: ${user.bizId} | 原因: 订单不存在" }
            throw IllegalArgumentException("订单不存在： $orderNo")
        } else if (supplierOrders.size > 1) {
            log.warn { "订单查询异常 | 订单号: $orderNo | 业务ID: ${user.bizId} | 原因: 匹配到多个订单 | 匹配订单数: ${supplierOrders.size}" }
            throw IllegalArgumentException("配配到多个订单号： ${supplierOrders.joinToString(";") { it.orderNo }}")
        } else {
            supplierOrder = supplierOrders.first()
            log.debug { "订单查询成功 | 订单号: ${supplierOrder.orderNo} | 主订单ID: ${supplierOrder.mainOrderId}" }
        }

        if (supplierOrder.product.supplierId != UserContextHolder.user!!.supplierId) {
            log.error {
                "订单查询失败 | 订单号: ${supplierOrder.orderNo} | 业务ID: ${user.bizId} | 原因: 订单不属于当前供应商 | 当前用户: $user | 订单供应商ID: ${supplierOrder.product.supplierId}"
            }
            throw IllegalStateException("无权限扫描此订单")
        }
        supplierOrder.scanAt = ZonedDateTime.now()

        try {
            val scanPackageResponse =
                supplierOrderPackage.scan(
                    supplierOrder.orderNo,
                    supplierOrder.shipping.wayBillRelation!!,
                    supplierOrder.shipping.waybillLabelUrl
                        ?: throw IllegalArgumentException("供应商订单里没有运单URL: ${supplierOrder.orderNo}"),
                    supplierOrder.shipping.channel.displayName,
                    shipMethod = supplierOrder.shipping.shipMethod ?: "",
                    supplierOrder.canPrintWayBillPdf,
                    supplierOrder.stopPrintWayBillWarning,
                ) { wayBillRelation ->
                    subOrderRepository
                        .findByShipping_WayBillRelationAndParentId(
                            wayBillRelation,
                            supplierOrder.mainOrderId!!,
                        ).map {
                            SameOrderPackageOrder(it.orderNo!!, it.product.title, it.product.qty)
                        }
                }

            supplierOrderPackageRepository.saveAndFlush(supplierOrderPackage)

            if (scanPackageResponse.waybillUrl != null) {
                val processTime = System.currentTimeMillis() - startTime
                log.info {
                    "包裹扫描完成 | 包裹ID: $packageId | 订单号: $orderNo | 运单号: ${supplierOrder.shipping.wayBillRelation} | 渠道: ${supplierOrder.shipping.channel.displayName} | 关联订单数: ${scanPackageResponse.orderNos?.size ?: 0} | 处理时间: ${processTime}ms"
                }
                // 扫描成功后直接将供应商订单状态设置为SHIPPED
                val orderNos = scanPackageResponse.orderNos ?: emptyList()
                if (orderNos.isNotEmpty()) {
                    val supplierOrders = supplierOrderRepository.findByOrderNoIn(orderNos)
                    supplierOrders.forEach { order ->
                        order.status = SupplierOrderStatus.SHIPPED
                    }
                    supplierOrderRepository.saveAllAndFlush(supplierOrders)
                    log.info {
                        "已将 ${supplierOrders.size} 个供应商订单状态更新为SHIPPED | 订单号: ${
                            orderNos.joinToString(
                                ",",
                            )
                        }"
                    }
                }
            }
            supplierOrderRepository.saveAndFlush(supplierOrder)

            // 出库判断逻辑：若扫描完成且所有供应商订单都已发货，则将运单状态置为出库
            if (scanPackageResponse.waybillUrl != null && supplierOrder.waybillId != null) {
                val waybillId = supplierOrder.waybillId!!
                log.info { "开始检查运单出库状态 | 运单ID: $waybillId | 订单号: $orderNo" }

                // 检查该运单下是否还有未发货的供应商订单
                val hasUnshippedOrders =
                    supplierOrderRepository.existsByWaybillIdAndStatusNot(
                        waybillId,
                        SupplierOrderStatus.SHIPPED,
                    )

                if (!hasUnshippedOrders) {
                    // 所有供应商订单都已发货，将运单状态置为出库
                    val waybill = waybillRepository.findByIdOrNull(waybillId)
                    if (waybill != null) {
                        applicationEventPublisher.publishEvent(WaybillOutBoundEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
                        waybillRepository.saveAndFlush(waybill)
                        log.info { "运单状态已更新为出库 | 运单ID: $waybillId | 运单号: ${waybill.waybillNo}" }
                    } else {
                        log.warn { "运单不存在 | 运单ID: $waybillId" }
                    }
                } else {
                    log.debug { "运单下仍有未发货的供应商订单，暂不设置为出库状态 | 运单ID: $waybillId" }
                }
            }

            return scanPackageResponse
        } catch (e: Exception) {
            val processTime = System.currentTimeMillis() - startTime
            log.error {
                "包裹扫描失败 | 包裹ID: $packageId | 订单号: $orderNo | 错误类型: ${e.javaClass.simpleName} | 错误信息: ${e.message} | 处理时间: ${processTime}ms"
            }
            throw e
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun completePackage(packageId: Long) {
        val user = UserContextHolder.user!!

        // 扫描完成后，将包裹状态改为完成
        val supplierOrderPackage =
            supplierOrderPackageRepository.findByIdOrNull(packageId)
                ?: throw IllegalArgumentException("包裹不存在： $packageId")
        supplierOrderPackage.markCompleted()
        supplierOrderPackageRepository.saveAndFlush(supplierOrderPackage)

        val packageContent = supplierOrderPackage.findUnCompletedOrders().toMutableMap()
        if (packageContent.isNotEmpty()) {
            log.info { "开始创建包裹： $packageContent" }
            val newSupplierOrderPackage =
                SupplierOrderPackage(
                    id = nextId(),
                    supplierId = supplierOrderPackage.supplierId,
                    packageContent = packageContent,
                    scanCount = packageContent.values.sumOf { it.scanCount },
                ).apply {
                    this.createdByName = user.name
                    this.updatedByName = user.name
                    this.createdBy = user.id
                    this.updatedBy = user.id
                }
            supplierOrderPackageRepository.saveAndFlush(newSupplierOrderPackage)
        }

        log.info { "createPackage success: $supplierOrderPackage" }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun forceCompleteBox(packageId: Long, wayBillRelation: String) {
        val user = UserContextHolder.user!!
        log.info { "强制完成箱子 | 包裹ID: $packageId | 运单关系: $wayBillRelation | 操作人: ${user.name}" }

        val supplierOrderPackage = supplierOrderPackageRepository.findByIdOrNull(packageId)
            ?: throw IllegalArgumentException("包裹不存在： $packageId")

        if (supplierOrderPackage.status == PackageStatus.COMPLETED) {
            throw IllegalArgumentException("包裹已经完成，不能强制完成箱子")
        }

        // 强制完成指定的箱子
        supplierOrderPackage.forceCompleteBox(wayBillRelation)
        supplierOrderPackageRepository.saveAndFlush(supplierOrderPackage)

        log.info { "箱子强制完成成功 | 包裹ID: $packageId | 运单关系: $wayBillRelation | 操作人: ${user.name}" }
    }

    fun pageQuery(req: PageQuerySubOrderPackageRequest): PageResponse<SupplierOrderPackage> {
        val user = UserContextHolder.user!!
        return if (user.supplierId != null) {
            supplierOrderPackageRepository
                .findBySupplierId(user.supplierId!!, req.orderNo, req.toPageable())
                .toResponse()
        } else {
            supplierOrderPackageRepository.findByOrderNo(req.orderNo, req.toPageable()).toResponse()
        }
    }

    fun findById(id: Long): SupplierOrderPackage? = supplierOrderPackageRepository.findByIdOrNull(id)

    @Transactional(rollbackFor = [Exception::class])
    fun update(
        id: Long,
        request: CreatePackageResponse,
    ) {
        val supplierOrderPackage =
            supplierOrderPackageRepository.findByIdOrNull(id)
                ?: throw IllegalArgumentException("包裹不存在： $id")
        supplierOrderPackage.packageNo = request.name
        supplierOrderPackageRepository.saveAndFlush(supplierOrderPackage)
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
