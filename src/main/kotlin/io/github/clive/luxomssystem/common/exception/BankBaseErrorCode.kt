package io.github.clive.luxomssystem.common.exception

import org.springframework.http.HttpStatus

enum class OmsBaseErrorCode(
    override val code: String,
    override val msg: String,
    override val httpCode: HttpStatus,
) : BaseErrorCodeEnum {
    ORDER_NO_REPEAT("100001", "订单号重复:%s", HttpStatus.BAD_REQUEST),
    UNVERIFIED("100002", "未认证", HttpStatus.UNAUTHORIZED),
    TEST_UNDER_LIMIT("100003", "测试次数已达上限", HttpStatus.BAD_REQUEST),
    CUSTOMER_NOT_FOUND("100004", "customer not found", HttpStatus.BAD_REQUEST),
    ORDER_REPEAT("100005", "repeat order:%s", HttpStatus.BAD_REQUEST),
    CUSTOMER_PLATFORM_ACCOUNT_NOT_FOUND("100006", "客户平台账户不存在", HttpStatus.BAD_REQUEST),
    ;

    companion object {
    }
}
