package io.github.clive.luxomssystem.domain.valueobject

import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.model.Spu
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.math.BigDecimal

@Embeddable
class ProductInfo {
    @Column(name = "spu", columnDefinition = "text")
    var spu: String? = null

    @Column(name = "size", columnDefinition = "text")
    var size: String? = null

    @Column(name = "color", columnDefinition = "text")
    var color: String? = null

    @Column(name = "qty")
    var qty: Int = 1

    @Column(name = "spu_id")
    var spuId: Long? = null

    @Column(name = "sku_id")
    var skuId: Long? = null

    @Column(name = "weight")
    var weight: BigDecimal = BigDecimal.ZERO

    @Column(name = "price")
    var price: BigDecimal = BigDecimal.ZERO

    @Column(name = "currency")
    var currency: String = ""

    @Column(name = "name")
    var name: String = ""

    @Column(name = "cn_name")
    var cnName: String = ""

    @Column(name = "title")
    var title: String = ""

    @Column(name = "supplier_id")
    var supplierId: Long? = null

    var supplierName: String? = null

    @Column(name = "detail", columnDefinition = "text")
    var detail: String? = null

    @Column(name = "custom_name", columnDefinition = "text")
    var customName: String? = null

    var material: String? = null

    var hsCode: String? = null

    fun skuCode(): String = "$spu-$size-$color"

    fun firstSetSku() = "$spu-${size!!.split("/")[0]}-$color"

    fun allSetSkus() = size!!.split("/").map { "$spu-$it-$color" }

    fun remainSetSkus() = size!!.split("/").map { "$spu-$it-$color" }.drop(1)

    fun setSizeList() = size!!.split("/")

    fun onSet() = "/" in size!!

    fun cannotDetermineSkuCodeReason(): String? =
        when {
            size == null && color == null -> {
                "尺寸和颜色"
            }

            color == null -> {
                "颜色"
            }

            size == null -> {
                "尺寸"
            }

            else -> {
                null
            }
        }

    fun determineSkuCode() =
        if (cannotDetermineSkuCodeReason() != null) {
            null
        } else {
            if (onSet()) firstSetSku() else skuCode()
        }

    fun update(
        sku: Sku?,
        spu: Spu?,
    ) {
        sku?.let {
            skuId = sku.id
            price = sku.purchaseCost
            currency = sku.purchaseCostCurrency
            weight = sku.weight
        }
        spu?.let {
            spuId = spu.id
            title = spu.title
            name = spu.name
            cnName = spu.cnName
            detail = spu.description
            if (hsCode.isNullOrBlank()) {
                hsCode = spu.hsCode
            }
        }
    }
}
