package io.github.clive.luxomssystem.domain.supplierOrder.model

import io.github.clive.luxomssystem.domain.doanload.base.AbstractBaseEntity
import io.github.clive.luxomssystem.facade.order.supplierorder.ScanPackageResponse
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.hypersistence.utils.hibernate.type.array.ListArrayType
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.*
import org.hibernate.annotations.Type

@Entity
@Table(name = "supplier_order_packages")
data class SupplierOrderPackage(
    @Id val id: Long = 0,
    val supplierId: Long,
    var packageNo: String = "pck-$id",
    var scanCount: Int = 0,
    // wayBillRelation to SamePackageOrder
    @Type(value = JsonType::class)
    @Column(name = "package_content", nullable = true, columnDefinition = "jsonb")
    val packageContent: MutableMap<String, SamePackageOrder> = mutableMapOf(),
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: PackageStatus = PackageStatus.CREATED,
    @Type(ListArrayType::class)
    @Column(
        name = "orderNos",
        columnDefinition = "text[]",
    )
    val orderNos: MutableList<String> = mutableListOf(),
) : AbstractBaseEntity() {
    fun markCompleted() {
        this.status = PackageStatus.COMPLETED
        this.updatedAt = System.currentTimeMillis()
        this.updatedByName = UserContextHolder.user!!.name
    }

    fun findUnCompletedOrders(): Map<String, SamePackageOrder> = packageContent.filter { !it.value.onComplete }

    fun forceCompleteBox(wayBillRelation: String) {
        val samePackageOrder = packageContent[wayBillRelation]
            ?: throw IllegalArgumentException("箱子不存在： $wayBillRelation")

        if (samePackageOrder.onComplete) {
            throw IllegalArgumentException("箱子已经完成，无需强制完成： $wayBillRelation")
        }

        samePackageOrder.forceComplete()
        this.updatedAt = System.currentTimeMillis()
        this.updatedByName = UserContextHolder.user!!.name
    }

    fun scan(
        orderNo: String,
        wayBillRelation: String,
        wayBillUrl: String,
        channel: String,
        shipMethod: String,
        canPrintWayBillPdf: Boolean = true,
        stopPrintWayBillWarning: String = "",
        fetchSubOrderFn: (String) -> List<SameOrderPackageOrder>,
    ): ScanPackageResponse {
        if (status == PackageStatus.CREATED) {
            status = PackageStatus.PROCESSING
        }

        if (status == PackageStatus.COMPLETED) {
            throw IllegalArgumentException("包裹已经完成，不能再扫描： $orderNo")
        }

        scanCount += 1

        val packageOrder =
            packageContent.computeIfAbsent(wayBillRelation) {
                fetchSubOrderFn(wayBillRelation).let { sameOrderPackageOrder ->
                    SamePackageOrder(
                        index = getNextAvailableIndex(),
                        channel,
                        shipMethod,
                        wayBillRelation,
                        wayBillUrl,
                        mutableMapOf(),
                        sameOrderPackageOrder.associateBy { it.orderNo }.toMutableMap(),
                        sameOrderPackageOrder.sumOf { it.count },
                        0,
                        false,
                        canPrintWayBillPdf,
                        stopPrintWayBillWarning,
                    )
                }
            }

        packageOrder.scan(orderNo)
        orderNos.add(orderNo)
        this.updatedAt = System.currentTimeMillis()
        this.updatedByName = UserContextHolder.user!!.name

        return when (packageOrder.onComplete) {
            true ->
                ScanPackageResponse(
                    packageOrder.wayBillUrl,
                    packageOrder.index,
                    packageOrder.canPrintWayBillPdf,
                    packageOrder.stopPrintWayBillWarning,
                    packageOrder.completed.values.map { it.orderNo },
                    packageOrder
                )

            false ->
                ScanPackageResponse(
                    null,
                    packageOrder.index,
                    packageOrder.canPrintWayBillPdf,
                    packageOrder.stopPrintWayBillWarning,
                    null,
                 packageOrder
                )
        }
    }

    private fun getNextAvailableIndex(): Int {
        val existingIndices = packageContent.values.map { it.index }.toSet()
        var nextIndex = 1
        while (existingIndices.contains(nextIndex)) {
            nextIndex++
        }
        return nextIndex
    }
}

enum class PackageStatus {
    CREATED,
    PROCESSING,
    COMPLETED,
}

data class SamePackageOrder(
    var index: Int,
    var channel: String,
    var shipMethod: String,
    var wayBillRelation: String,
    var wayBillUrl: String,
    // orderNo to SameOrderPackageOrder
    var completed: MutableMap<String, SameOrderPackageOrder>,
    var nonCompleted: MutableMap<String, SameOrderPackageOrder>,
    var totalCount: Int,
    var scanCount: Int,
    var onComplete: Boolean,
    var canPrintWayBillPdf: Boolean = true,
    var stopPrintWayBillWarning: String = "",
) {
    fun scan(orderNo: String) {
        scanCount += 1
        if (completed.containsKey(orderNo)) {
            throw IllegalArgumentException("该订单数量已经充足，不能再扫描： $orderNo")
        } else {
            val sameOrderPackageOrder =
                nonCompleted[orderNo]
                    ?: throw IllegalArgumentException(
                        "当前订单不存在： $orderNo 对于订单关系： $wayBillRelation",
                    )
            sameOrderPackageOrder.scan()
            if (sameOrderPackageOrder.onComplete) {
                completed[orderNo] = sameOrderPackageOrder
                nonCompleted.remove(orderNo)
            }
        }
        if (nonCompleted.isEmpty()) {
            onComplete = true
        }
    }

    fun forceComplete() {
        // 将所有未完成的订单强制标记为完成
        nonCompleted.values.forEach { order ->
            order.forceComplete()
            completed[order.orderNo] = order
        }
        nonCompleted.clear()
        onComplete = true
    }
}

data class SameOrderPackageOrder(
    var orderNo: String,
    var title: String = "",
    var count: Int,
    var scanCount: Int = 0,
    var onComplete: Boolean = false,
) {
    fun scan() {
        this.scanCount += 1
        if (this.scanCount == this.count) {
            this.onComplete = true
        }
    }

    fun forceComplete() {
        this.scanCount = this.count
        this.onComplete = true
    }
}
