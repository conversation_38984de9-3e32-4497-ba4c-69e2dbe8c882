package io.github.clive.luxomssystem.facade.spu.response

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.domain.spu.model.Spu

data class SPUPageResponse(
    val id: Long,
    val spuCode: String,
    val title: String,
    val name: String,
    val cnName: String,
    val category: String,
    val productImage: String,
    val packageQuantity: Int,
    val showImages: List<String>,
    val description: String,
    val sizes: List<String>,
    val colors: List<String>,
    val status: BaseStatus,
    val hsCode: String? = null,
    val createdByName: String? = null,
    val updatedByName: String? = null,
    val createdAt: Long? = null,
    val updatedAt: Long? = null,
) {
    companion object {
        fun fromDomain(spu: Spu): SPUPageResponse =
            SPUPageResponse(
                id = spu.id,
                spuCode = spu.spuCode,
                title = spu.title,
                name = spu.name,
                cnName = spu.cnName,
                category = spu.category,
                productImage = spu.productImage,
                packageQuantity = spu.packageQuantity,
                showImages = spu.showImages.toList(),
                description = spu.description,
                sizes = spu.sizes.toList(),
                colors = spu.colors.toList(),
                status = spu.status,
                hsCode = spu.hsCode,
                createdAt = spu.createdAt,
                updatedAt = spu.updatedAt,
                createdByName = spu.createdByName,
                updatedByName = spu.updatedByName,
            )
    }
}
