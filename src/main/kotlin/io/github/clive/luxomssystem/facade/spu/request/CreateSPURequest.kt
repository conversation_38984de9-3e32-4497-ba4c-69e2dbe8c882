package io.github.clive.luxomssystem.facade.spu.request

import io.github.clive.luxomssystem.facade.sku.request.UpdateSkuRequest
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty

data class CreateSPURequest(
    @field:NotBlank(message = "SPU code is required")
    val spuCode: String,
    @field:NotBlank(message = "Title is required")
    val title: String,
    @field:NotBlank(message = "Name is required")
    val name: String,
    @field:NotBlank(message = "Chinese name is required")
    val cnName: String,
    @field:NotBlank(message = "Category is required")
    val category: String,
    val productImage: String = "",
    @field:Min(1, message = "same package quantity: 一个包裹能放几个")
    val packageQuantity: Int,
    val showImages: List<String> = emptyList(),
    val description: String = "",
    @field:NotEmpty(message = "At least one size is required")
    val sizes: List<String>,
    @field:NotEmpty(message = "At least one color is required")
    val colors: List<String>,
    val hsCode: String? = null,
    val skus: List<UpdateSkuRequest> = emptyList(),
)
