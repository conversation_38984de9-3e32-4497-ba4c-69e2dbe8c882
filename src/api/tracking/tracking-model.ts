// 轨迹相关类型定义

export interface TrackingInfoResponse {
    id: number;
    orderNos: string[];
    waybillId?: number;
    waybillNo: string;
    trackingNumber?: string;
    channel: string;
    currentStatus: string;
    currentStatusDisplay: string;
    destinationCountry?: string;
    originCountry?: string;
    lastMileProvider?: LastMileProviderResponse;
    trackingEvents: TrackingEventResponse[];
    deliveryDays?: number;
    podLinks: string[];
    lastUpdatedAt: string;
    lastEventTime?: string;
}

export interface LastMileProviderResponse {
    name: string;
    telephone?: string;
    website?: string;
}

export interface TrackingEventResponse {
    eventTime: string;
    status: string;
    statusDisplay: string;
    description: string;
    location?: string;
    isLastMileEvent: boolean;
}

export interface TrackingUpdateResponse {
    success: boolean;
    skipped: boolean;
    message: string;
    trackingInfo?: TrackingInfoResponse;
}

export interface BatchTrackingUpdateResponse {
    successCount: number;
    failureCount: number;
    skippedCount: number;
    errors: string[];
}

export interface TrackingStatisticsResponse {
    totalTracking: number;
    completedTracking: number;
    pendingTracking: number;
    statusCounts: Record<string, number>;
    channelCounts: Record<string, number>;
}

export interface CleanupResponse {
    deletedCount: number;
    message: string;
}

export interface ChannelInfo {
    code: string;
    name: string;
    supported: boolean;
}

// 轨迹状态枚举 - 与后端 TrackingStatus.kt 保持一致
export const TrackingStatus = {
    NOT_FOUND: { code: 'NOT_FOUND', display: '未找到' },
    PRE_ADVICE_RECEIVED: { code: 'PRE_ADVICE_RECEIVED', display: '电子预报' },
    PICKED_UP: { code: 'PICKED_UP', display: '已揽收' },
    IN_TRANSIT: { code: 'IN_TRANSIT', display: '运输途中' },
    ARRIVED_DESTINATION_COUNTRY: { code: 'ARRIVED_DESTINATION_COUNTRY', display: '到达目的地国家' },
    IN_CUSTOMS: { code: 'IN_CUSTOMS', display: '清关中' },
    CUSTOMS_CLEARED: { code: 'CUSTOMS_CLEARED', display: '清关完成' },
    ARRIVED_FOR_PICKUP: { code: 'ARRIVED_FOR_PICKUP', display: '到达待取' },
    OUT_FOR_DELIVERY: { code: 'OUT_FOR_DELIVERY', display: '派送中' },
    DELIVERY_FAILED: { code: 'DELIVERY_FAILED', display: '投递失败' },
    DELIVERED: { code: 'DELIVERED', display: '已签收' },
    EXCEPTION: { code: 'EXCEPTION', display: '异常' },
    RETURNED: { code: 'RETURNED', display: '已退回' },
    CANCELLED: { code: 'CANCELLED', display: '已取消' },
    UNKNOWN: { code: 'UNKNOWN', display: '未知状态' }
} as const;

// 时间范围选项
export const TimeRangeOptions = {
    LAST_7_DAYS: { value: '7', label: '最近7天' },
    LAST_30_DAYS: { value: '30', label: '最近30天' },
    LAST_90_DAYS: { value: '90', label: '最近90天' },
    CUSTOM: { value: 'custom', label: '自定义' }
} as const;

// 渠道枚举
export const WaybillChannel = {
    YUNTU: { code: 'YUNTU', display: '云途' },
    YW_HZ: { code: 'YW_HZ', display: '燕文-杭州' },
    YW_QZ: { code: 'YW_QZ', display: '燕文-泉州' },
    YW_GZ: { code: 'YW_GZ', display: '燕文-广州' },
    YW_YW: { code: 'YW_YW', display: '燕文-义乌' }
} as const;
