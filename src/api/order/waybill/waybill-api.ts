import {CancelStopPrintRequest, FinanceStatisticResponse, StopPrintRequest, UpdateWaybillRequest, WaybillPageResponse, WaybillTaxSetRequest} from "@/api/order/waybill/waybill-model";
import {FetchParams, FetchResponse} from "@/components/table/base-table";
import apiClient from "@/lib/apiClient";

export async function pageWaybill(params: FetchParams): Promise<FetchResponse<WaybillPageResponse>> {
    return await apiClient.get({
        url: '/api/waybills/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        },
    })
}

export async function failedPageWaybill(params: FetchParams): Promise<FetchResponse<WaybillPageResponse>> {
    return await apiClient.get({
        url: '/api/waybills/failed/page',
        params: {
            ...params.pagination,
            ...params.searchParams
        },
    })
}

export async function updateWaybillStatus(waybillId: string, req: UpdateWaybillRequest) {
    return await apiClient.put({
        url: '/api/waybills/' + waybillId,
        data: req,
    })
}


export async function retryWaybill(waybillId: string) {
    return await apiClient.post({
        url: '/api/waybills/' + waybillId + '/retry',
    })
}

export async function cancelWaybill(waybillId: string, reason?: string) {
    return await apiClient.post({
        url: '/api/waybills/' + waybillId + '/:cancel',
        params: {
            reason: reason || ''
        }
    })
}


export async function countWaybillSupplierOrderScanned(waybillIds: string[]) {
    return await apiClient.post<Record<string, { scanned: number, total: number }>>({
        url: '/api/waybills/supplier-scanned-count',
        data: waybillIds
    })
}

export async function retryWaybillBatch(waybillIds: string[]) {
    return await apiClient.post({
        url: '/api/waybills/retry/batch',
        data: waybillIds
    })
}

export async function getWaybillStatusCount(select: string): Promise<any[]> {
    return await apiClient.get({
        url: '/api/waybills/day/status/count',
        params: {
            select: select
        }
    })
}

export async function deleteBatchWaybill(waybillIds: string[]) {
    return await apiClient.delete({
        url: '/api/waybills/batch',
        data: waybillIds
    })
}


export async function staticWaybill(mainOrderId: string): Promise<FinanceStatisticResponse> {
    return await apiClient.get({
        url: '/api/waybills/statistic/finance?mainOrderId=' + mainOrderId,
    })
}


export async function setWaybillTax(req: WaybillTaxSetRequest) {
    return await apiClient.post({
        url: '/api/waybills/quick-channel/tax:set',
        data: req,
    })
}


export async function changeWaybillChannel(ids: string[], channel: string, method: string) {
    return await apiClient.post({
        url: '/api/waybills/channel:change',
        data: {
            ids,
            channel,
            method,
        },
    })
}


export async function stopWaybillPrint(data: StopPrintRequest) {
    return await apiClient.post({
        url: `/api/waybills/stop/print`,
        data
    })
}


export async function cancelStopWaybillPrint(data: CancelStopPrintRequest) {
    return await apiClient.post({
        url: `/api/waybills/stop/print/cancel`,
        data
    })
}

export async function exportWaybillExcel(waybillIds: string[], fileName: string) {
    return await apiClient.post({
        url: `/api/waybills/export/excel`,
        data: {
            ids: waybillIds,
            fileName
        },
        responseType: 'blob'
    })
}


export async function extendWaybill(id: string, newOrderNo?: string): Promise<string> {
    return await apiClient.post<string>({
        url: `/api/waybills/extend`,
        data: {
            id,
            newOrderNo
        }
    })
}

export async function submitWaybill(id: string, waybillNo: string) {
    return await apiClient.post({
        url: `/api/waybills/${id}/submit`,
        data: {
            waybillNo
        }
    })
}