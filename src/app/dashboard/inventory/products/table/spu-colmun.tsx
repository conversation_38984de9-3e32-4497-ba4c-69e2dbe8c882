import { deleteSpu } from "@/api/inventory/spu/spu-api";
import TableActions, { Action, ActionType } from "@/components/table/table-actions";
import { SpuPageResponse } from "@/api/inventory/spu/spu-model";
import { refreshTableAtom, userInfoAtom } from "@/state/common";
import { Row } from "@tanstack/react-table";
import { ColumnDef } from "@tanstack/table-core";
import { useAtomValue, useSetAtom } from "jotai";
import React from "react";
import { toast } from "sonner";
import { CopyText } from "@/components/buttons/copy-text-btn";
import { match } from "ts-pattern";
import { Chip } from "@/components/buttons/chip";
import { FormattedTime } from "@/components/common/formatted-time";
import { Link, useNavigate } from "react-router-dom";
import { Barcode, Box, Calendar, Info, Package2, Palette, Ruler, Tag } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { DoubleCheckBtn } from "@/components/buttons/double-check-btn";
import { ImagePreviewDialog } from "@/components/buttons/image-preview";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";

interface SpuActionsProps {
    row: Row<SpuPageResponse>;
}

export const SpuActions: React.FC<SpuActionsProps> = ({ row }) => {

    const setRefresh = useSetAtom(refreshTableAtom);

    const navigate = useNavigate();

    const handleView = React.useCallback(() => {
        navigate(`/dashboard/products/details?id=${row.getValue("id")}`);
    }, [navigate, row]);

    const handleEdit = React.useCallback(() => {
        navigate(`/dashboard/products/details?id=${row.getValue("id")}`);
    }, [navigate, row]);

    const handleDelete = React.useCallback(async () => {
        try {
            await deleteSpu(row.getValue("id"));
            toast.success("删除成功");
            // 可能需要在这里触发一些状态更新或者重新获取数据
            setRefresh((prev) => prev + 1)
        } catch (error) {
            toast.error("删除失败");
            console.error("删除SPU时出错:", error);
        }
    }, [row, setRefresh]);

    const rowActions: Action<SpuPageResponse>[] = React.useMemo(() => [
        {
            type: ActionType.DIRECT,
            label: '查看',
            onClick: handleView,
        },
        {
            type: ActionType.MODAL,
            label: '编辑',
            openModal: handleEdit,
        },
        {
            type: ActionType.ALERT,
            label: '删除',
            alertTitle: '确认删除',
            alertDescription: '你确定要删除这条记录吗？此操作不可逆。',
            onClick: handleDelete,
        },
    ], [handleView, handleEdit, handleDelete]);

    return <TableActions actions={rowActions} row={row} />;
};

export const SpuColumns: ColumnDef<SpuPageResponse>[] = [
    {
        accessorKey: "id",
        header: "基础信息",
        cell: ({ row }) => (
            <div className="space-y-1">
                {/* <div className="flex items-center space-x-2">
                    <Hash className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{row.getValue("id")}</span>
                </div> */}
                <div className="flex items-center space-x-2">
                    <Barcode className="h-4 w-4 text-gray-500" />
                    <CopyText value={row.original.spuCode} />
                </div>
            </div>
        ),
        meta: {
            title: '基础信息',
        },
    },
    {
        accessorKey: "title",
        header: "商品信息",
        cell: ({ row }) => (
            <div className="space-y-1">
                <div className="flex items-center space-x-2">
                    <Tag className="h-4 w-4 text-blue-500" />
                    <CopyText value={row.original.title} />
                </div>
                <div className="text-sm text-gray-500">
                    {row.original.name} | {row.original.cnName}
                </div>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Box className="h-4 w-4" />
                    <span>{row.original.category}</span>
                </div>
            </div>
        ),
        meta: {
            title: '商品信息',
        },
    },
    {
        accessorKey: "productImage",
        header: "图片信息",
        cell: ({ row }) => {
            const [isPreviewOpen, setIsPreviewOpen] = React.useState(false);
            const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);
            const allImages = [row.original.productImage, ...row.original.showImages];

            return (
                <>
                    <div className="flex space-x-4">
                        <div className="w-16 h-16 cursor-pointer" onClick={() => {
                            setSelectedImageIndex(0);
                            setIsPreviewOpen(true);
                        }}>
                            <img
                                src={row.original.productImage}
                                alt={row.original.name}
                                className="w-full h-full object-cover rounded-lg shadow-sm hover:opacity-80 transition-opacity"
                            />
                        </div>
                        <div className="flex space-x-1">
                            {(row.original.showImages).slice(0, 3).map((image, index) => (
                                <div
                                    key={index}
                                    className="cursor-pointer"
                                    onClick={() => {
                                        setSelectedImageIndex(index + 1);
                                        setIsPreviewOpen(true);
                                    }}
                                >
                                    <img
                                        src={image}
                                        alt={`展示图片 ${index + 1}`}
                                        className="w-12 h-12 object-cover rounded-lg shadow-sm hover:opacity-80 transition-opacity"
                                    />
                                </div>
                            ))}
                            {(row.original.showImages).length > 3 && (
                                <div className="w-12 h-12 bg-gray-100 rounded-lg shadow-sm flex items-center justify-center text-sm text-gray-500">
                                    +{(row.original.showImages).length - 3}
                                </div>
                            )}
                        </div>
                    </div>

                    <ImagePreviewDialog
                        images={allImages}
                        currentIndex={selectedImageIndex}
                        isOpen={isPreviewOpen}
                        onClose={() => setIsPreviewOpen(false)}
                        onNavigate={setSelectedImageIndex}
                    />
                </>
            );
        },
        meta: {
            title: '图片信息',
        },
    },
    {
        accessorKey: "specifications",
        header: "规格信息",
        cell: ({ row }) => (
            <div className="space-y-2">
                <div className="flex items-center space-x-2">
                    <Package2 className="h-4 w-4 text-orange-500" />
                    <span>{row.original.packageQuantity} 件/包</span>
                </div>
                <div className="flex items-center space-x-2">
                    <Ruler className="h-4 w-4 text-purple-500" />
                    <div className="flex flex-wrap gap-1">
                        {(row.original.sizes).map((size, index) => (
                            <span key={index} className="px-2 py-0.5 bg-purple-50 text-purple-700 rounded-full text-xs">
                                {size}
                            </span>
                        ))}
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <Palette className="h-4 w-4 text-pink-500" />
                    <div className="flex flex-wrap gap-1">
                        {(row.original.colors).map((color, index) => (
                            <span key={index} className="px-2 py-0.5 bg-pink-50 text-pink-700 rounded-full text-xs">
                                {color}
                            </span>
                        ))}
                    </div>
                </div>
            </div>
        ),
        meta: {
            title: '规格信息',
        },
    },
    {
        accessorKey: "status",
        header: "状态",
        cell: ({ row }) => {
            return match(row.original.status)
                .with('DISABLED', () => (
                    <div className="flex items-center space-x-2">
                        <Chip label="禁用" variant="red" />
                    </div>
                ))
                .with('ENABLED', () => (
                    <div className="flex items-center space-x-2">
                        <Chip label="启用" variant="green" />
                    </div>
                ))
                .otherwise(() => (
                    <div className="flex items-center space-x-2">
                        <Chip label="未知状态" variant="purple" />
                    </div>
                ));
        },
        meta: {
            title: '状态',
        },
    },
    {
        accessorKey: "timeInfo",
        header: () => (
            <div className="flex items-center space-x-2 min-w-[200px]">
                <Calendar className="h-4 w-4 text-gray-500 mt-0.5" />
                <span>时间信息</span>
            </div>
        ),
        cell: ({ row }) => (
            <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                        <div className="text-gray-600">创建: <FormattedTime timestamp={row.original.createdAt} formatter='DD/MM/YY HH:mm' /></div>
                        <div className="text-gray-500 text-xs">由 {row.original.createdByName}</div>
                    </div>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                        <div className="text-gray-600">更新: <FormattedTime timestamp={row.original.updatedAt} formatter='DD/MM/YY HH:mm' /></div>
                        <div className="text-gray-500 text-xs">由 {row.original.updatedByName}</div>
                    </div>
                </div>
            </div>
        ),
        meta: {
            title: '时间信息',
        },
    },
    {
        accessorKey: "description",
        header: () => (
            <div className="flex items-center space-x-2 !max-w-[100px]">
                <Info className="h-4 w-4 text-gray-500 mt-0.5" />
                <span>描述</span>
            </div>
        ),
        cell: ({ row }) => (
            <div className="flex items-start space-x-2">
                <Popover>
                    <PopoverTrigger>
                        <div className="max-w-[100px] truncate cursor-pointer hover:text-blue-500">
                            {row.original.description}
                        </div>
                    </PopoverTrigger>
                    <PopoverContent className="w-[300px] p-4">
                        <div className="text-sm text-gray-700 whitespace-pre-wrap">
                            {row.original.description || '暂无描述'}
                        </div>
                    </PopoverContent>
                </Popover>
            </div>
        ),
        meta: {
            title: '描述',
        },
    }
    , {
        accessorKey: "actions",
        header: "操作",
        cell: ({ row }) => {
            const setRefreshTable = useSetAtom(refreshTableAtom);
            const userInfo = useAtomValue(userInfoAtom);
            const isBizUser = userInfo?.roleId === "123196957025374208"

            if (isBizUser) {
                return null;
            }
            return (
                <div className='flex items-center gap-2'>
                    <Button
                        variant="link"
                        className="h-auto p-0 text-blue-600 text-xs" asChild>
                        <Link to={`/dashboard/inventory/products/details?id=${row.original.id}`}>
                            编辑
                        </Link>
                    </Button>
                    <Separator orientation='vertical' className='h-4' />
                    <DoubleCheckBtn
                        className='h-auto p-0 text-red-600 text-xs'
                        variant='link'
                        title='删除商品'
                        description='删除商品将影响相关库存，请谨慎操作'
                        onConfirm={() => {
                            toast.promise(
                                deleteSpu(row.original.id),
                                {
                                    loading: '删除中...',
                                    success: () => {
                                        setRefreshTable(prev => prev + 1);
                                        return '删除成功';
                                    },
                                    error: '删除失败',
                                }
                            );
                        }}
                        buttonText='删除'
                        onCancel={() => { }}
                    />
                </div>
            )
        },
        meta: {
            title: '操作',
        },
    }
];