import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import DecimalInput from "@/components/inputs/demical-input";

export type SKU = {
    id?: string;
    skuCode: string;
    size: string;
    color: string;
    purchaseCost: number;
    purchaseCostCurrency: string;
    salePrice: number;
    weight: number;
    volume: number;
    status?: string;
    updatedAt?: number;
    createdAt?: number;
};

type SKUTableProps = {
    skus: SKU[];
    onSkuChange: (index: number, field: keyof SKU, value: string | number) => void;
    onBulkEdit: (field: keyof SKU, value: string | number) => void;
};

const SKUTable: React.FC<SKUTableProps> = ({ skus, onSkuChange, onBulkEdit }) => {
    const [bulkEditValues, setBulkEditValues] = React.useState({
        purchaseCost: 0,
        purchaseCostCurrency: '',
        salePrice: 0.01,
        weight: 0.01,
        volume: 0.01
    });

    const handleBulkEdit = (field: keyof SKU, value: string | number) => {
        setBulkEditValues(prev => ({ ...prev, [field]: value }));
        onBulkEdit(field, value);
    };

    return (
        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead className="w-60">SKU 编码</TableHead>
                    <TableHead className="w-20">尺寸</TableHead>
                    <TableHead className="w-20">颜色</TableHead>
                    <TableHead>采购成本</TableHead>
                    <TableHead>币种</TableHead>
                    <TableHead>销售价格</TableHead>
                    <TableHead>重量 (kg)</TableHead>
                    <TableHead>体积 (m³)</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <TableRow>
                    <TableCell colSpan={3}>批量编辑：</TableCell>
                    <TableCell>
                        <DecimalInput
                            value={bulkEditValues.purchaseCost}
                            onChange={(value) => handleBulkEdit('purchaseCost', value)}
                            placeholder="批量设置采购成本"
                            min={0}
                        />
                    </TableCell>
                    <TableCell>
                        <Select
                            value={bulkEditValues.purchaseCostCurrency}
                            onValueChange={(value) => handleBulkEdit('purchaseCostCurrency', value)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="批量设置币种" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="USD">USD</SelectItem>
                                <SelectItem value="CNY">CNY</SelectItem>
                                <SelectItem value="EUR">EUR</SelectItem>
                                <SelectItem value="GBP">GBP</SelectItem>
                                <SelectItem value="AUD">AUD</SelectItem>
                                <SelectItem value="JPY">JPY</SelectItem>
                                <SelectItem value="HKD">HKD</SelectItem>
                                <SelectItem value="SGD">SGD</SelectItem>
                            </SelectContent>
                        </Select>
                    </TableCell>
                    <TableCell>
                        <DecimalInput
                            value={bulkEditValues.salePrice}
                            onChange={(value) => handleBulkEdit('salePrice', value)}
                            placeholder="批量设置销售价格"
                        />
                    </TableCell>
                    <TableCell>
                        <DecimalInput
                            value={bulkEditValues.weight}
                            onChange={(value) => handleBulkEdit('weight', value)}
                            placeholder="批量设置重量"
                        />
                    </TableCell>
                    <TableCell>
                        <DecimalInput
                            value={bulkEditValues.volume}
                            onChange={(value) => handleBulkEdit('volume', value)}
                            placeholder="批量设置体积"
                        />
                    </TableCell>
                </TableRow>
                {skus.map((sku, index) => (
                    <TableRow key={sku.id || `${sku.size}-${sku.color}`}>
                        <TableCell>{sku.skuCode}</TableCell>
                        <TableCell>{sku.size}</TableCell>
                        <TableCell>{sku.color}</TableCell>
                        <TableCell>
                            <DecimalInput
                                value={sku.purchaseCost}
                                onChange={(value) => onSkuChange(index, 'purchaseCost', value)}
                                placeholder="输入采购成本"
                            />
                        </TableCell>
                        <TableCell>
                            <Select
                                value={sku.purchaseCostCurrency}
                                onValueChange={(value) => onSkuChange(index, 'purchaseCostCurrency', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue>{sku.purchaseCostCurrency}</SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="USD">USD</SelectItem>
                                    <SelectItem value="CNY">CNY</SelectItem>
                                    <SelectItem value="EUR">EUR</SelectItem>
                                    <SelectItem value="GBP">GBP</SelectItem>
                                    <SelectItem value="AUD">AUD</SelectItem>
                                    <SelectItem value="JPY">JPY</SelectItem>
                                    <SelectItem value="HKD">HKD</SelectItem>
                                    <SelectItem value="SGD">SGD</SelectItem>
                                </SelectContent>
                            </Select>
                        </TableCell>
                        <TableCell>
                            <DecimalInput
                                value={sku.salePrice}
                                onChange={(value) => onSkuChange(index, 'salePrice', value)}
                                placeholder="输入销售价格"
                            />
                        </TableCell>
                        <TableCell>
                            <DecimalInput
                                value={sku.weight}
                                onChange={(value) => onSkuChange(index, 'weight', value)}
                                placeholder="输入重量"
                            />
                        </TableCell>
                        <TableCell>
                            <DecimalInput
                                value={sku.volume}
                                onChange={(value) => onSkuChange(index, 'volume', value)}
                                placeholder="输入体积"
                            />
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
    );
};

export default React.memo(SKUTable);