# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

The **oms-lux** is the React-based frontend application for the LUX Order Management System. It provides a modern,
responsive web interface for managing orders, customers, suppliers, inventory, and logistics operations. This frontend
complements the Kotlin/Spring Boot backend API.

## Common Development Commands

### Running the Application

```bash
# Install dependencies (choose one based on lock file)
bun install          # Recommended (fastest)
npm install          # Alternative
pnpm install         # Alternative
yarn install         # Alternative

# Start development server
bun run dev          # Runs on http://localhost:5173
npm run dev          # Alternative

# Build for production
bun run build --mode production
npm run build        # Alternative

# Preview production build
bun run preview
```

### Code Quality

```bash
# Run ESLint
bun run lint
npm run lint

# TypeScript type checking (happens during build)
tsc --noEmit
```

### Deployment

Production deployment via SCP (from README):

```bash
scp -P 2046 -r dist/* ubuntu@**************:/mnt/efs/luxoms/lux-oms-ui
scp -r dist/* ubuntu@*************:/mnt/efs/luxoms/lux-oms-ui-sz
```

## Architecture & Code Style

### Directory Structure

```
src/
├── api/              # API service layer (organized by domain)
│   ├── model.ts      # TypeScript interfaces for API responses
│   └── [domain]/     # Domain-specific API calls
├── app/              # Application pages/routes
│   └── dashboard/    # Main app pages (customers, orders, etc.)
├── components/       # Reusable UI components
│   ├── ui/          # Base UI components (shadcn/ui)
│   ├── modals/      # Modal components
│   ├── table/       # Data table components
│   └── layout/      # Layout components
├── hooks/           # Custom React hooks
├── lib/             # Utilities and configuration
├── routers/         # Route configuration
└── state/           # State management (Jotai atoms)
```

### TypeScript Standards

- **Strict Mode**: Always use TypeScript strict mode
- **Interface Naming**: Prefix interfaces with 'I' (e.g., `ICustomer`)
- **Type Safety**: Avoid `any` type; use proper typing for all API responses
- **Import Paths**: Use `@/` alias for src imports (e.g., `@/components/ui/button`)

### React Component Guidelines

When creating new components:

1. **Check existing components first** - especially in `src/components/ui/`
2. Use functional components with TypeScript
3. Follow naming convention: PascalCase for components, camelCase for functions
4. Place component-specific types in the same file
5. Use shadcn/ui components as base building blocks

### Import Order

```typescript
// 1. React and third-party libraries
import React from 'react'
import {useForm} from 'react-hook-form'

// 2. UI components from shadcn/ui
import {Button} from '@/components/ui/button'
import {Input} from '@/components/ui/input'

// 3. Application components
import {CustomerTable} from '@/components/table/customer-table'

// 4. API and utilities
import {getCustomers} from '@/api/customer'
import {formatDate} from '@/lib/utils'

// 5. Types and interfaces
import type {ICustomer} from '@/api/model'
```

## Key Technologies

### UI Framework

- **shadcn/ui**: Component library built on Radix UI
- **Tailwind CSS**: Utility-first styling
- **Lucide React**: Icon library

### State Management

- **Jotai**: Atomic state management for global state
- **React Hook Form**: Form state management
- **Tanstack Query**: Server state management (if used)

### Data Handling

- **Tanstack Table**: Advanced data tables
- **Zod**: Schema validation
- **Axios**: HTTP client with interceptors

### Build Tools

- **Vite**: Fast build tool with HMR
- **TypeScript 5.5.3**: Type safety
- **PostCSS**: CSS processing with Tailwind

## API Integration

### Base Configuration

- API client configured in `src/lib/api.ts`
- Uses Axios with JWT authentication
- Base URL from environment variable: `VITE_API_URL`

### API Structure

All API calls follow domain-driven organization:

- `/api/customer/` - Customer management
- `/api/order/` - Order operations
- `/api/supplier/` - Supplier management
- `/api/inventory/` - Product catalog (SPU/SKU)
- `/api/tracking/` - Logistics and tracking
- `/api/user/` - Authentication and user management

### Authentication Flow

- JWT tokens stored in localStorage
- Automatic token injection via Axios interceptors
- Token refresh handled in API client

## Component Patterns

### Data Tables

Use Tanstack Table with shadcn/ui components:

```typescript
// Define columns in separate file (e.g., customer-columns.tsx)
// Use DataTable component from components/table/
// Implement sorting, filtering, and pagination
```

### Forms

Use React Hook Form with Zod validation:

```typescript
// Define schema with Zod
// Use Form components from shadcn/ui
// Handle submission with proper error handling
```

### Modals

- Place modal components in `src/components/modals/`
- Use Dialog component from shadcn/ui
- Manage state with parent component or URL params

## Environment Variables

Required environment variables:

- `VITE_API_URL` - Backend API base URL

## Development Best Practices

### Component Development

1. Start by checking if similar components exist
2. Use shadcn/ui components as building blocks
3. Keep components focused and single-purpose
4. Extract reusable logic into custom hooks

### API Calls

1. Define types in `src/api/model.ts`
2. Create API functions in domain-specific files
3. Handle errors consistently
4. Use proper loading states

### State Management

1. Use Jotai atoms for global state
2. Keep component state local when possible
3. Use React Hook Form for all forms
4. Avoid prop drilling with context or atoms

### Performance

1. Use React.memo for expensive components
2. Implement proper loading states
3. Use pagination for large data sets
4. Lazy load routes and heavy components

## Common Tasks

### Adding a New Page

1. Create component in `src/app/dashboard/[feature]/`
2. Add route in router configuration
3. Update navigation if needed
4. Implement data fetching and state management

### Creating API Integration

1. Add types to `src/api/model.ts`
2. Create API functions in `src/api/[domain]/`
3. Use consistent error handling
4. Add proper TypeScript types

### Working with Tables

1. Define columns in `[feature]-columns.tsx`
2. Use DataTable component
3. Implement filtering and sorting
4. Add actions column for operations