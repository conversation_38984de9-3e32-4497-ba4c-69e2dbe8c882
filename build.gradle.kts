import org.apache.sshd.client.SshClient
import org.apache.sshd.client.channel.ClientChannelEvent
import org.apache.sshd.client.config.hosts.HostConfigEntryResolver
import org.apache.sshd.client.session.ClientSession
import org.apache.sshd.common.keyprovider.FileKeyPairProvider
import org.apache.sshd.sftp.client.SftpClient
import org.apache.sshd.sftp.client.SftpClientFactory
import java.net.InetSocketAddress
import java.nio.file.Files
import java.nio.file.Path
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.EnumSet
import kotlin.concurrent.thread


buildscript {
    dependencies {
        classpath("org.apache.sshd:sshd-sftp:2.15.0")
    }
}

plugins {
    kotlin("jvm") version "1.9.25"
    kotlin("plugin.spring") version "1.9.25"
    id("org.springframework.boot") version "3.3.4"
    id("io.spring.dependency-management") version "1.1.6"
    kotlin("plugin.jpa") version "1.9.25"
}

group = "io.github.clive"
version = "0.0.1-SNAPSHOT"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

repositories {
    mavenCentral()
}

dependencies {
    implementation(libs.kt.log)
    implementation(libs.jdbc.pg)
    implementation(libs.easyexcel)
    implementation(libs.qcloud.cos)
    implementation(libs.hibernate.get63())
    implementation("org.springframework.boot:spring-boot-starter-validation") // 必须的验证依赖
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("com.fasterxml.jackson.module:jackson-module-kotlin")
    implementation("org.jetbrains.kotlinx:kotlinx-coroutines-core:1.7.3") // 根据需要调整版本
    implementation("org.jetbrains.kotlin:kotlin-reflect")
    implementation("com.itextpdf:itextpdf:5.5.13.3")
    implementation("com.google.zxing:core:3.4.1")
    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    runtimeOnly("io.micrometer:micrometer-registry-prometheus")
    implementation("io.opentelemetry:opentelemetry-api:1.31.0")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.jetbrains.kotlin:kotlin-test-junit5")
    testRuntimeOnly("org.junit.platform:junit-platform-launcher")

    implementation("com.github.gavlyukovskiy:datasource-proxy-spring-boot-starter:1.9.1")
}

kotlin {
    compilerOptions {
        freeCompilerArgs.addAll("-Xjsr305=strict")
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

allOpen {
    annotation("jakarta.persistence.Entity")
    annotation("jakarta.persistence.MappedSuperclass")
    annotation("jakarta.persistence.Embeddable")
}

val targetDockerWorkDir = "/root/"

val defaultVerifyTimeout: Duration = Duration.ofSeconds(30)

fun ClientSession.executeCommandAndWaitFor(
    verifyTimeout: Duration = defaultVerifyTimeout,
    command: String,
) {
    createExecChannel(command)
        .use { channel ->
            channel.open().verify(verifyTimeout)
            thread {
                channel.invertedErr?.transferTo(System.err)
            }
            thread {
                channel.invertedOut?.transferTo(System.out)
            }
            channel.waitFor(EnumSet.of(ClientChannelEvent.CLOSED), -1)
        }
}

fun withSSH(
    sftpBlock: (SftpClient.() -> Unit)? = null,
    sshBlock: ClientSession.() -> Unit,
) {
    val privateKeyFile = rootProject.projectDir.resolve("lux-sh.pem")
    val client = SshClient.setUpDefaultClient()
    client.hostConfigEntryResolver = HostConfigEntryResolver.EMPTY
    client.start()

    client
        .connect("root", InetSocketAddress("**************", 22))
        .verify(defaultVerifyTimeout)
        .session
        .use { session ->
            session.keyIdentityProvider = FileKeyPairProvider(privateKeyFile.toPath())
            session.auth().verify(defaultVerifyTimeout)

            if (sftpBlock != null) {
                val sftpClient = SftpClientFactory.instance().createSftpClient(session)
                sftpClient.nameDecodingCharset = Charsets.UTF_8
                sftpBlock(sftpClient)
            }

            sshBlock(session)
        }
}

fun SftpClient.uploadFile(
    localFile: Path,
    remoteDir: String = targetDockerWorkDir,
    remoteFilename: String? = null,
) {
    val useName = remoteFilename ?: localFile.fileName.toString()
    openRemoteFileChannel(
        remoteDir + useName,
        EnumSet.of(SftpClient.OpenMode.Write, SftpClient.OpenMode.Create, SftpClient.OpenMode.Truncate),
    ).use { remote ->
        Files.newByteChannel(localFile).use { local ->
            remote.transferFrom(local, 0, Long.MAX_VALUE)
        }
    }
}

tasks.register("uploadProd") {
    group = "remote"
    dependsOn(tasks.bootJar)
    actions.add {
        val localFile =
            tasks.bootJar
                .get()
                .archiveFile
                .get()
                .asFile
                .toPath()
        val localFileNameWithoutExtension = localFile.fileName.toString().removeSuffix(".jar")
        val now = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy_MM_dd_HH_mm_ss"))
        val newFileName = "$localFileNameWithoutExtension-$now.jar"
        withSSH(
            {
                uploadFile(
                    tasks.bootJar
                        .get()
                        .archiveFile
                        .get()
                        .asFile
                        .toPath(),
                    remoteFilename = newFileName,
                )
            },
        ) {
        }
    }
}

tasks.register("launch") {
    group = "remote"
    actions.add {
        withSSH {
            executeCommandAndWaitFor(command = "source /etc/profile && ./launch-lux-oms-system.sh")
        }
    }
}
